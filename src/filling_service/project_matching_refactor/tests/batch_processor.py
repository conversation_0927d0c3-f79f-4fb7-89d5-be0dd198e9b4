import asyncio
from concurrent.futures import ThreadPoolExecutor
import pandas as pd

from src.filling_service.project_matching_refactor.core.orchestrator import match_document_with_case

# List of document IDs to process
doc_ids = [135, 136, 137, 142, 143, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296]  # Add more IDs as needed

# ThreadPoolExecutor for running sync matching in parallel
executor = ThreadPoolExecutor(max_workers=10)  # Adjust number of workers

async def run_match(doc_id):
    loop = asyncio.get_event_loop()
    try:
        # Run the matching function in a thread
        result = await loop.run_in_executor(executor, match_document_with_case, doc_id)

        # Unpack the full result tuple
        project_id, manual_review, extracted_client_name, matched_client_name = result

        return {
            "doc_id": doc_id,
            "project_id": project_id,
            "manual_review": manual_review,
            "extracted_client_name": extracted_client_name,
            "matched_client_name": matched_client_name
        }

    except Exception as e:
        return {
            "doc_id": doc_id,
            "error": str(e),
            "project_id": None,
            "manual_review": None,
            "extracted_client_name": None,
            "matched_client_name": None
        }

async def main():
    tasks = [run_match(doc_id) for doc_id in doc_ids]
    results = await asyncio.gather(*tasks)

    # Save to Excel
    df = pd.DataFrame(results)
    df.to_excel("matched_documents_with_case.xlsx", index=False)
    print("✅ Results saved to matched_documents.xlsx")

if __name__ == "__main__":
    asyncio.run(main())
