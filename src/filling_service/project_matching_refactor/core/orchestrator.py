from src.filling_service.config import extractor, doc_case_matcher
from src.filling_service.project_matching_refactor.configuration.company_config import case_number_synonyms
from src.filling_service.project_matching_refactor.configuration.constants import FILEVINE_VITALS_DESCRIPTIONS
from src.filling_service.project_matching_refactor.extraction.email_extractor import get_email_info
from src.filling_service.project_matching_refactor.models.model_builder import build_extended_model_with_identifier
from src.filling_service.project_matching_refactor.models.model_factory import map_identifiers_to_data_destination, \
    get_identifier_description_for_company
from src.filling_service.project_matching_refactor.utils.model_utils import unpack_model_fields
from src.filling_service.prompts.identifier_fields_extractor import extract_identifier_fields_prompt


def match_document_with_case(doc_id: int, company_data_destination_id: int = 6):

    # 1. Get email body
    email_info = get_email_info(doc_id)
    print(f"Email info: {email_info}")

    # 2. Determine which primary identifier should be extracted form the email based on company case naming convention and company_id
    # TODO implement new methods
    # company_id = doc_processor.get_company_id_by_document_id(doc_id)
    # primary_identifier = doc_processor.get_primary_identifier_for_company(company_id)
    # print(f"Primary identifier: {primary_identifier}")
    # Used for testing purposes
    primary_identifier = 'Client Name'

    # 3. Get vitals fields based on company_data_destination_id
    company_data_destination_id = 6
    base_model = map_identifiers_to_data_destination(company_data_destination_id)
    # print(f"\n\nIdentifiers model: {base_model.model_json_schema()}\n\n")

    primary_identifier_description = get_identifier_description_for_company(company_id=1)
    # secondary_identifiers = get_secondary_identifiers_for_company(company_id=1)

    # 4. Add dynamic field(s) and return extended model
    extended_model = build_extended_model_with_identifier(
        base_model,
        primary_identifier,
        model_name="FilingMetadataWithProjectID"
    )
    print(f"\nExtended model schema:\n{extended_model.model_json_schema()}\n")


    # 5. Prepare prompt
    prompt = extract_identifier_fields_prompt(
        case_number_synonyms=case_number_synonyms,
        primary_field=primary_identifier,
        primary_identifier_definition=primary_identifier_description,
        secondary_identifiers=FILEVINE_VITALS_DESCRIPTIONS
    )

    # print(f"\n\n Prompt: {prompt}")

    # 6. Extract fields
    raw_values = extractor.extract_field_values(email_info, prompt,
                                                            extended_model)
    print(f"\n\n Extracted field values: {raw_values}")
    print(f"Type: {type(raw_values)}")
    # 7. Unpack results
    extracted_field_values = extended_model(**raw_values)
    unpacked_values = unpack_model_fields(extracted_field_values)
    print(f"\nUnpacked values:\n{unpacked_values}")

    # if unpacked_values.get("case_number"):
    #     print(f"Case number: {unpacked_values.get('case_number')}")
    #     # project_id = project_matcher.match_on_case_number(case_number, company_data_destination_id)
    #     if project_id:
    #         print(f"Matched project ID: {project_id}")
    # project_id, manual_review = doc_case_matcher.match_project(
    #     unpacked_values=unpacked_values,
    #     company_data_destination_id=company_data_destination_id
    # )
    # if manual_review:
    #     print("⚠️ Manual review required for project matching.")
    # else:
    #     print(f"✅ Final matched project ID: {project_id}")
    #
    # return project_id
    project_id, manual_review, extracted_client_name, matched_client_name = doc_case_matcher.match_project(
        company_data_destination_id=company_data_destination_id,
        unpacked_values=unpacked_values
    )

    return project_id, manual_review, extracted_client_name, matched_client_name


# 268
# result = match_document_with_case(135)
# print("Function completed successfully!")

