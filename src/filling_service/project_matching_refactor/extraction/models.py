from pydantic import BaseModel, Field

# class FileVineVitals(BaseModel):
#     # project_id: str = Field(default="N/A", alias="Project ID")
#     case_number: str = Field(default="N/A", alias="Case Number")
#     incident_date: str = Field(default="N/A", alias="Incident Date")
#     incident_type: str = Field(default="N/A", alias="Incident Type")
#     lead_attorney: str = Field(default="N/A", alias="Lead Attorney")
#     first_primary: str = Field(default="N/A", alias="First Primary")
#     project_number: str = Field(default="N/A", alias="Project Number")  # Same as project_id
#     sol_date_only: str = Field(default="N/A", alias="SOL (date only)")
#     meds_total_balance_due: str = Field(default="N/A", alias="Meds Total Balance Due (currency)")
#     phase_name: str = Field(default="N/A", alias="Phase Name")
#     project_email_address: str = Field(default="N/A", alias="project email address")
#     project_or_client_name: str = Field(default="N/A", alias="project name or client name or both")

class FileVineVitals(BaseModel):
    case_number: str
    incident_date: str
    incident_type: str
    lead_attorney: str
    first_primary: str
    project_number: str
    sol_date_only: str
    meds_total_balance_due: str
    phase_name: str
    project_email_address: str
    project_or_client_name: str


# Main Identifiers PS
class ClientName(BaseModel):
    client_name: str
    explanation: str    # used for testing purposes, remove later

# Main Identifiers MK
class DefendantPlaintiff(BaseModel):
    defendant: str
    plaintiff: str
