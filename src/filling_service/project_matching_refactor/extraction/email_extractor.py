from src.filling_service.project_matching_refactor.utils.text_utils import extract_plain_text_from_email_body
from src.shared.service_registry import doc_processor
from src.shared.utils.logger_config import setup_logger

logger = setup_logger(__name__)

def get_email_info(doc_id: int) -> str:
    """
    Retrieves and formats email metadata and body for LLM-based project matching.

    Args:
        doc_id (int): The document ID to process.

    Returns:
        str: A formatted string representation of the email metadata and content.
    """
    email_info = doc_processor.get_document_info(doc_id)
    if not email_info:
        raise ValueError("Failed to retrieve document metadata.")

    metadata = email_info.get("metadata", {})
    subject = metadata.get("subject", "")
    plain_text = extract_plain_text_from_email_body(email_info)

    sender = metadata.get("sender", "")
    sent_from = metadata.get("from", "")
    to_recipients = metadata.get("to_recipients", "")
    cc_recipients = metadata.get("cc_recipients", "")

    formatted = (
        f"Subject: {subject}\n"
        f"Sender: {sender}\n"
        f"Sent From: {sent_from}\n"
        f"To Recipients: {to_recipients}\n"
        f"CC Recipients: {cc_recipients}\n\n"
        f"Email Body:\n{plain_text}"
    )

    logger.debug(f"[EMAIL INFO] Formatted email content for doc ID {doc_id}:\n{formatted}")
    return formatted

# # Test the function with document ID 268
# result = get_email_info(268)
# print("Function completed successfully!")
# print(result)