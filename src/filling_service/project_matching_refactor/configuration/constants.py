FILEVINE_VITALS_DESCRIPTIONS = {
    # "case_number": "The official court case number, clearly labeled as 'Case Number' in the document. If Case NUmber is not present look for Case Number synonyms",
    "incident_date": "The date when the incident related to the case occurred (e.g., accident date).",
    "incident_type": "A description of the type of incident involved (e.g., car accident, slip and fall).",
    "lead_attorney": "The name of the primary attorney assigned to the case.",
    "first_primary": "The name of the first listed primary party in the Filevine project record.",
    "project_number": "The internal project number assigned to the case (sometimes same as 'Project ID').",
    "sol_date_only": "The statute of limitations date — the deadline by which legal action must be filed.",
    "meds_total_balance_due": "The total outstanding balance due for medical expenses, if listed.",
    "phase_name": "The current phase or stage of the case (e.g., 'Discovery', 'Litigation').",
    "project_email_address": "The email address associated with the Filevine project for communications.",
    "project_or_client_name": "The name of the project, the client, or both, depending on Filevine configuration.",
}
