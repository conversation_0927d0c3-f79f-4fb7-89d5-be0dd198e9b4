from bs4 import BeautifulSoup
import re

def extract_plain_text_from_email_body(email_data):
    """
    Extracts plain text content from the email_body field of an email dictionary.

    Args:
        email_data (dict): The dictionary containing email information.

    Returns:
        str: The extracted plain text, or a message if extraction fails.
    """
    # First check if email_body is directly in email_data
    email_body = email_data.get("email_body")

    # If not found, check in metadata
    if not email_body:
        metadata = email_data.get("metadata", {})
        email_body = metadata.get("email_body")

    # Check if email_body exists and is a dictionary
    if not email_body or not isinstance(email_body, dict):
        return "Error: 'email_body' not found or not a dictionary."

    content = email_body.get("content")
    content_type = email_body.get("content_type")

    # Check if content exists and is a string
    if not content or not isinstance(content, str):
         # Return empty string if content is missing or not a string
        return ""

    if content_type == "html":
        try:
            soup = BeautifulSoup(content, "html.parser")
            text = soup.get_text(separator='\n')
            text = re.sub(r'\n\s*\n', '\n\n', text)
            text = text.strip()

            return text
        except Exception as e:
            # Handle potential parsing errors
            return f"Error parsing HTML: {e}"
    elif content_type == "plain":
        return content.strip()
    else:
        return f"Unsupported content type: {content_type}. Returning raw"