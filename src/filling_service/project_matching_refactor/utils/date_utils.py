from datetime import datetime

def normalize_date_for_db(input_date: str) -> str:
    """
    Converts various date formats to a normalized 'YYYY-MM-DD' string.
    """
    if not input_date or input_date == "N/A":
        return "N/A"

    formats = [
        "%Y-%m-%d",                      # 2025-06-12
        "%Y-%m-%dT%H:%M:%S.%f",          # 2025-06-12T00:00:00.000000
        "%Y-%m-%dT%H:%M:%S.%f0",         # In case extra 0 gets appended
        "%m/%d/%Y",                      # 06/12/2025
        "%m/%d/%Y %I:%M %p",             # 06/12/2025 8:00 AM
        "%m/%d/%Y %H:%M",                # 06/12/2025 13:00
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(input_date, fmt)
            return dt.strftime("%Y-%m-%d")
        except ValueError:
            continue

    raise ValueError(f"Unrecognized date format: {input_date}")