from typing import Type
from pydantic import BaseModel, Field, create_model

def build_extended_model_with_identifier(
    base_model: Type[BaseModel],
    primary_identifier: str,
    model_name: str = "MetadataExtractorModel"
) -> Type[BaseModel]:
    """
    Builds a new Pydantic model by extending the base model with a primary identifier field.

    Args:
        base_model: The base Pydantic model to extend.
        primary_identifier: The field label (e.g., "Client Name") to be added dynamically.
        model_name: The name of the extended model class.

    Returns:
        A new Pydantic model class with the additional field.
    """
    field_name = primary_identifier.lower().replace(" ", "_")
    print(f"Field name: {field_name}")
    dynamic_fields = {
        field_name: (
            str,
            Field(default="N/A", alias=field_name)
        )
    }
    print(f"Dynamic fields: {dynamic_fields}")
    return create_model(model_name, __base__=base_model, **dynamic_fields)

# Sample Usage
# model = build_extended_model_with_identifier(
#     FileVineVitals, "Client Name", "FilingMetadataWithClientName")
# print(model.model_json_schema())
