from typing import Type
from pydantic import BaseModel
from src.filling_service.project_matching_refactor.configuration.company_config import DATA_DESTINATION_MODEL_MAP, \
    COMPANY_ID_TO_IDENTIFIER_DESCRIPTION


# Get base model based on data destination
def map_identifiers_to_data_destination(company_data_destination_id: int) -> Type[BaseModel]:
    model = DATA_DESTINATION_MODEL_MAP.get(company_data_destination_id)
    if not model:
        raise ValueError(f"No model found for data destination ID {company_data_destination_id}")
    return model

# Get primary identifier description
def get_identifier_description_for_company(company_id: int) -> str:
    return COMPANY_ID_TO_IDENTIFIER_DESCRIPTION.get(
        company_id,
        "No description available for this company."
    )
